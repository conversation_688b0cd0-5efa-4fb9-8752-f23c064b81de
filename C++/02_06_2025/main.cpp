#include <iostream>
#include <stdio.h>
#include <string>
using namespace std;

// Абстрактний клас "Домашня тварина"
class Pet {
protected:
    string name;
    string characteristics;

public:
    // Конструктор для встановлення імені та характеристик
    Pet(const string& petName, const string& petCharacteristics)
        : name(petName), characteristics(petCharacteristics) {}

    // Чисто віртуальні функції
    virtual void Sound() = 0;  // Видає звук тварини
    virtual void Show() = 0;   // Виводить ім'я тварини
    virtual void Type() = 0;   // Виводить назву підвиду

    // Віртуальний деструктор
    virtual ~Pet() = default;
};

// Клас "Собака"
class Dog : public Pet {
public:
    Dog(const string& name, const string& characteristics)
        : Pet(name, characteristics) {}

    void Sound() override {
        cout << "Гав-гав! Собака " << name << " гавкає.\n";
    }

    void Show() override {
        cout << "Ім'я тварини: " << name << "\n";
        cout << "Характеристики: " << characteristics << "\n";
    }

    void Type() override {
        cout << "Підвид: Собака (Canis lupus familiaris)\n";
    }
};

// Клас "Кішка"
class Cat : public Pet {
public:
    Cat(const string& name, const string& characteristics)
        : Pet(name, characteristics) {}

    void Sound() override {
        cout << "Мяу-мяу! Кішка " << name << " нявкає.\n";
    }

    void Show() override {
        cout << "Ім'я тварини: " << name << "\n";
        cout << "Характеристики: " << characteristics << "\n";
    }

    void Type() override {
        cout << "Підвид: Кішка (Felis catus)\n";
    }
};

// Клас "Папуга"
class Parrot : public Pet {
public:
    Parrot(const string& name, const string& characteristics)
        : Pet(name, characteristics) {}

    void Sound() override {
        cout << "Кар-кар! Папуга " << name << " каркає та повторює слова.\n";
    }

    void Show() override {
        cout << "Ім'я тварини: " << name << "\n";
        cout << "Характеристики: " << characteristics << "\n";
    }

    void Type() override {
        cout << "Підвид: Папуга (Psittaciformes)\n";
    }
};

// Клас "Хом'як"
class Hamster : public Pet {
public:
    Hamster(const string& name, const string& characteristics)
        : Pet(name, characteristics) {}

    void Sound() override {
        cout << "Піп-піп! Хом'як " << name << " пищить.\n";
    }

    void Show() override {
        cout << "Ім'я тварини: " << name << "\n";
        cout << "Характеристики: " << characteristics << "\n";
    }

    void Type() override {
        cout << "Підвид: Хом'як (Cricetinae)\n";
    }
};

int main() {
    // Створення об'єктів різних тварин
    Dog dog("Рекс", "Великий, дружелюбний, коричневого кольору");
    Cat cat("Мурка", "Пухнаста, сіра, любить спати");
    Parrot parrot("Кеша", "Яскраво-зелений, розмовляє, любить горіхи");
    Hamster hamster("Хома", "Маленький, рудий, активний вночі");

    // Масив вказівників на базовий клас для демонстрації поліморфізму
    Pet* pets[] = {&dog, &cat, &parrot, &hamster};

    cout << "=== ДЕМОНСТРАЦІЯ РОБОТИ З ДОМАШНІМИ ТВАРИНАМИ ===\n\n";

    // Демонстрація роботи кожної тварини
    for (int i = 0; i < 4; i++) {
        cout << "--- Тварина " << (i + 1) << " ---\n";
        pets[i]->Type();
        pets[i]->Show();
        pets[i]->Sound();
        cout << "\n";
    }

    cout << "=== ОКРЕМІ ВИКЛИКИ МЕТОДІВ ===\n\n";

    // Демонстрація окремих викликів
    cout << "Собака:\n";
    dog.Type();
    dog.Show();
    dog.Sound();

    cout << "\nКішка:\n";
    cat.Type();
    cat.Show();
    cat.Sound();

    return 0;
}
